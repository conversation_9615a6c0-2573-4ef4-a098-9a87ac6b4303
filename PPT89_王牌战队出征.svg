<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 背景装饰图案 - 军队出征的抽象表现 -->
  <g opacity="0.05">
    <rect x="100" y="100" width="1720" height="880" rx="50" fill="#1e3a8a"/>
  </g>
  
  <!-- 装饰性光芒 -->
  <g opacity="0.1">
    <circle cx="960" cy="540" r="400" fill="none" stroke="#fbbf24" stroke-width="3"/>
    <circle cx="960" cy="540" r="500" fill="none" stroke="#fbbf24" stroke-width="2"/>
    <circle cx="960" cy="540" r="600" fill="none" stroke="#fbbf24" stroke-width="1"/>
  </g>
  
  <!-- 主标题 -->
  <g transform="translate(960, 450)">
    <rect x="-400" y="-100" width="800" height="200" rx="30" fill="#ffffff" filter="url(#shadow)" opacity="0.95"/>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="96" font-weight="bold" fill="url(#titleGradient)" filter="url(#glow)">
      王牌战队，出征！
    </text>
  </g>
  
  <!-- 底部激励文字 -->
  <g transform="translate(960, 750)">
    <rect x="-500" y="-40" width="1000" height="80" rx="20" fill="#1e3a8a" filter="url(#shadow)" opacity="0.9"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="300" fill="#ffffff">
      期待你们，带着胜利和荣耀，凯旋！
    </text>
  </g>
  
  <!-- 装饰性旗帜元素 -->
  <g opacity="0.3">
    <!-- 左侧旗帜 -->
    <g transform="translate(300, 300)">
      <rect x="0" y="0" width="4" height="200" fill="#dc2626"/>
      <path d="M4,0 L80,20 L4,40 Z" fill="#dc2626"/>
      <path d="M4,50 L80,70 L4,90 Z" fill="#fbbf24"/>
      <path d="M4,100 L80,120 L4,140 Z" fill="#dc2626"/>
    </g>
    
    <!-- 右侧旗帜 -->
    <g transform="translate(1620, 300)">
      <rect x="0" y="0" width="4" height="200" fill="#dc2626"/>
      <path d="M0,0 L-76,20 L0,40 Z" fill="#dc2626"/>
      <path d="M0,50 L-76,70 L0,90 Z" fill="#fbbf24"/>
      <path d="M0,100 L-76,120 L0,140 Z" fill="#dc2626"/>
    </g>
  </g>
  
  <!-- 装饰性箭头 -->
  <g transform="translate(960, 600)" opacity="0.4">
    <path d="M-200,0 L200,0 M180,-30 L200,0 L180,30" stroke="#dc2626" stroke-width="8" fill="none"/>
    <path d="M-200,50 L200,50 M180,20 L200,50 L180,80" stroke="#fbbf24" stroke-width="6" fill="none"/>
    <path d="M-200,-50 L200,-50 M180,-80 L200,-50 L180,-20" stroke="#dc2626" stroke-width="6" fill="none"/>
  </g>
  
  <!-- 左右装饰星星 -->
  <g opacity="0.2">
    <!-- 左侧星星 -->
    <g transform="translate(200, 200)">
      <path d="M0,-20 L6,-6 L20,-6 L10,2 L16,16 L0,8 L-16,16 L-10,2 L-20,-6 L-6,-6 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(150, 400)">
      <path d="M0,-15 L4.5,-4.5 L15,-4.5 L7.5,1.5 L12,12 L0,6 L-12,12 L-7.5,1.5 L-15,-4.5 L-4.5,-4.5 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(250, 600)">
      <path d="M0,-12 L3.6,-3.6 L12,-3.6 L6,1.2 L9.6,9.6 L0,4.8 L-9.6,9.6 L-6,1.2 L-12,-3.6 L-3.6,-3.6 Z" fill="#fbbf24"/>
    </g>
    
    <!-- 右侧星星 -->
    <g transform="translate(1720, 250)">
      <path d="M0,-20 L6,-6 L20,-6 L10,2 L16,16 L0,8 L-16,16 L-10,2 L-20,-6 L-6,-6 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1770, 450)">
      <path d="M0,-15 L4.5,-4.5 L15,-4.5 L7.5,1.5 L12,12 L0,6 L-12,12 L-7.5,1.5 L-15,-4.5 L-4.5,-4.5 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1670, 650)">
      <path d="M0,-12 L3.6,-3.6 L12,-3.6 L6,1.2 L9.6,9.6 L0,4.8 L-9.6,9.6 L-6,1.2 L-12,-3.6 L-3.6,-3.6 Z" fill="#fbbf24"/>
    </g>
  </g>
  
  <!-- 顶部和底部装饰线 -->
  <path d="M200,150 Q960,100 1720,150" stroke="#dc2626" stroke-width="4" fill="none" opacity="0.3"/>
  <path d="M200,930 Q960,980 1720,930" stroke="#fbbf24" stroke-width="4" fill="none" opacity="0.3"/>
</svg>
