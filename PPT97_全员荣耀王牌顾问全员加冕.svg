<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="certificateGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fef3c7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="6" stdDeviation="12" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#fbbf24" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#dc2626" stroke-width="3" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)" filter="url(#glow)">
    全员荣耀：王牌顾问，全员加冕！
  </text>
  
  <!-- 结业证书 -->
  <g transform="translate(960, 400)" filter="url(#shadow)">
    <!-- 证书主体 -->
    <rect x="-400" y="-150" width="800" height="300" rx="20" fill="url(#certificateGradient)" stroke="#fbbf24" stroke-width="4"/>
    
    <!-- 证书装饰边框 -->
    <rect x="-380" y="-130" width="760" height="260" rx="15" fill="none" stroke="#dc2626" stroke-width="2" stroke-dasharray="10,5"/>
    
    <!-- 证书标题 -->
    <text x="0" y="-80" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">
      结业证书
    </text>
    <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#1e3a8a">
      Certificate of Completion
    </text>
    
    <!-- 证书内容 -->
    <text x="0" y="0" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">
      兹证明持证人已成功完成
    </text>
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#1e3a8a">
      《聚势·智变·赢未来——中国联通"智家通通"数智化营销王牌战队实战营》
    </text>
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#374151">
      全部课程，特此证明。
    </text>
    
    <!-- 证书印章位置 -->
    <circle cx="280" cy="80" r="40" fill="#dc2626" opacity="0.1"/>
    <text x="280" y="85" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#dc2626">
      [印章]
    </text>
    
    <!-- 证书日期 -->
    <text x="-280" y="100" font-family="Microsoft YaHei" font-size="16" fill="#6b7280">
      颁发日期：2024年__月__日
    </text>
  </g>
  
  <!-- 中央激励文字 -->
  <g transform="translate(960, 650)">
    <rect x="-500" y="-60" width="1000" height="120" rx="20" fill="#ffffff" filter="url(#shadow)" opacity="0.95"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" font-weight="bold" fill="#dc2626">
      毕业，不是结束，而是出征的开始！
    </text>
  </g>
  
  <!-- 底部身份标识 -->
  <g transform="translate(960, 800)">
    <rect x="-600" y="-40" width="1200" height="80" rx="20" fill="#1e3a8a" filter="url(#shadow)"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#ffffff">
      从此，你们拥有一个共同的名字：
    </text>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#fbbf24">
      中国联通"智家通通"王牌营销顾问
    </text>
  </g>
  
  <!-- 装饰性皇冠 -->
  <g transform="translate(960, 200)" opacity="0.3">
    <path d="M-40,0 L-20,-30 L0,-20 L20,-30 L40,0 L30,20 L-30,20 Z" fill="#fbbf24"/>
    <circle cx="-20" cy="-30" r="5" fill="#dc2626"/>
    <circle cx="0" cy="-20" r="6" fill="#dc2626"/>
    <circle cx="20" cy="-30" r="5" fill="#dc2626"/>
  </g>
  
  <!-- 装饰性星星 -->
  <g opacity="0.4">
    <!-- 左侧星星群 -->
    <g transform="translate(200, 300)">
      <path d="M0,-20 L6,-6 L20,-6 L10,2 L16,16 L0,8 L-16,16 L-10,2 L-20,-6 L-6,-6 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(150, 500)">
      <path d="M0,-15 L4.5,-4.5 L15,-4.5 L7.5,1.5 L12,12 L0,6 L-12,12 L-7.5,1.5 L-15,-4.5 L-4.5,-4.5 Z" fill="#dc2626"/>
    </g>
    <g transform="translate(250, 700)">
      <path d="M0,-12 L3.6,-3.6 L12,-3.6 L6,1.2 L9.6,9.6 L0,4.8 L-9.6,9.6 L-6,1.2 L-12,-3.6 L-3.6,-3.6 Z" fill="#fbbf24"/>
    </g>
    
    <!-- 右侧星星群 -->
    <g transform="translate(1720, 350)">
      <path d="M0,-18 L5.4,-5.4 L18,-5.4 L9,1.8 L14.4,14.4 L0,7.2 L-14.4,14.4 L-9,1.8 L-18,-5.4 L-5.4,-5.4 Z" fill="#dc2626"/>
    </g>
    <g transform="translate(1770, 550)">
      <path d="M0,-16 L4.8,-4.8 L16,-4.8 L8,1.6 L12.8,12.8 L0,6.4 L-12.8,12.8 L-8,1.6 L-16,-4.8 L-4.8,-4.8 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1670, 750)">
      <path d="M0,-14 L4.2,-4.2 L14,-4.2 L7,1.4 L11.2,11.2 L0,5.6 L-11.2,11.2 L-7,1.4 L-14,-4.2 L-4.2,-4.2 Z" fill="#dc2626"/>
    </g>
  </g>
  
  <!-- 装饰性光芒 -->
  <g opacity="0.2">
    <path d="M960,250 L940,300 L980,300 Z" fill="#fbbf24"/>
    <path d="M960,250 L920,320 L1000,320 Z" fill="#fbbf24" opacity="0.7"/>
    <path d="M960,250 L900,340 L1020,340 Z" fill="#fbbf24" opacity="0.5"/>
  </g>
</svg>
