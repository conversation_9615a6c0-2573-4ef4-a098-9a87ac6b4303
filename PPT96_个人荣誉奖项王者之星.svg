<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,120 Q480,80 960,120 T1920,120" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,960 Q480,1000 960,960 T1920,960" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)">
    个人荣誉奖项：王者之星
  </text>
  
  <!-- 三个奖项版块 -->
  <g transform="translate(0, 200)">
    <!-- 金牌营销之星 -->
    <g transform="translate(320, 150)">
      <rect x="0" y="0" width="480" height="300" rx="20" fill="#ffffff" filter="url(#shadow)"/>
      <!-- 金牌图标 -->
      <g transform="translate(240, 80)">
        <circle cx="0" cy="0" r="40" fill="#fbbf24"/>
        <circle cx="0" cy="0" r="30" fill="#f59e0b"/>
        <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">🥇</text>
      </g>
      <text x="240" y="160" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#fbbf24">
        金牌营销之星
      </text>
      <text x="240" y="190" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        (个人总业绩第一)
      </text>
      <text x="240" y="220" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1e3a8a">
        获奖者：XXX
      </text>
      <!-- 获奖者照片占位 -->
      <rect x="190" y="240" width="100" height="40" rx="5" fill="#f3f4f6" stroke="#fbbf24" stroke-width="2" stroke-dasharray="5,5"/>
      <text x="240" y="265" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#9ca3af">
        [获奖者照片]
      </text>
    </g>
    
    <!-- 最佳案例贡献奖 -->
    <g transform="translate(720, 150)">
      <rect x="0" y="0" width="480" height="300" rx="20" fill="#ffffff" filter="url(#shadow)"/>
      <!-- 银牌图标 -->
      <g transform="translate(240, 80)">
        <circle cx="0" cy="0" r="40" fill="#c0c0c0"/>
        <circle cx="0" cy="0" r="30" fill="#a3a3a3"/>
        <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">🥈</text>
      </g>
      <text x="240" y="160" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#6b7280">
        最佳案例贡献奖
      </text>
      <text x="240" y="190" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        (复盘分享最有价值)
      </text>
      <text x="240" y="220" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1e3a8a">
        获奖者：XXX
      </text>
      <!-- 获奖者照片占位 -->
      <rect x="190" y="240" width="100" height="40" rx="5" fill="#f3f4f6" stroke="#c0c0c0" stroke-width="2" stroke-dasharray="5,5"/>
      <text x="240" y="265" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#9ca3af">
        [获奖者照片]
      </text>
    </g>
    
    <!-- 最具潜力新人奖 -->
    <g transform="translate(1120, 150)">
      <rect x="0" y="0" width="480" height="300" rx="20" fill="#ffffff" filter="url(#shadow)"/>
      <!-- 铜牌图标 -->
      <g transform="translate(240, 80)">
        <circle cx="0" cy="0" r="40" fill="#cd7f32"/>
        <circle cx="0" cy="0" r="30" fill="#b8860b"/>
        <text x="0" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">🥉</text>
      </g>
      <text x="240" y="160" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#cd7f32">
        最具潜力新人奖
      </text>
      <text x="240" y="190" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        (进步最快)
      </text>
      <text x="240" y="220" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#1e3a8a">
        获奖者：XXX
      </text>
      <!-- 获奖者照片占位 -->
      <rect x="190" y="240" width="100" height="40" rx="5" fill="#f3f4f6" stroke="#cd7f32" stroke-width="2" stroke-dasharray="5,5"/>
      <text x="240" y="265" text-anchor="middle" font-family="Microsoft YaHei" font-size="14" fill="#9ca3af">
        [获奖者照片]
      </text>
    </g>
  </g>
  
  <!-- 颁奖词区域 -->
  <g transform="translate(960, 650)">
    <rect x="-600" y="-80" width="1200" height="160" rx="20" fill="#f8fafc" filter="url(#shadow)"/>
    <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">
      颁奖词
    </text>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#374151">
      一个伟大的团队，离不开杰出的个人英雄。
    </text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#374151">
      他们用自己的专业和努力，为团队的胜利贡献了不可替代的力量！
    </text>
    <text x="0" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#374151">
      让我们为这些杰出的个人英雄，送上最热烈的掌声！
    </text>
  </g>
  
  <!-- 装饰性星星 -->
  <g opacity="0.3">
    <g transform="translate(150, 250)">
      <path d="M0,-15 L4.5,-4.5 L15,-4.5 L7.5,1.5 L12,12 L0,6 L-12,12 L-7.5,1.5 L-15,-4.5 L-4.5,-4.5 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1770, 300)">
      <path d="M0,-12 L3.6,-3.6 L12,-3.6 L6,1.2 L9.6,9.6 L0,4.8 L-9.6,9.6 L-6,1.2 L-12,-3.6 L-3.6,-3.6 Z" fill="#c0c0c0"/>
    </g>
    <g transform="translate(100, 500)">
      <path d="M0,-18 L5.4,-5.4 L18,-5.4 L9,1.8 L14.4,14.4 L0,7.2 L-14.4,14.4 L-9,1.8 L-18,-5.4 L-5.4,-5.4 Z" fill="#cd7f32"/>
    </g>
    <g transform="translate(1820, 550)">
      <path d="M0,-10 L3,-3 L10,-3 L5,1 L8,8 L0,4 L-8,8 L-5,1 L-10,-3 L-3,-3 Z" fill="#fbbf24"/>
    </g>
  </g>
  
  <!-- 装饰性光芒 -->
  <g opacity="0.2">
    <path d="M560,300 L580,280 M560,300 L580,320 M560,300 L540,280 M560,300 L540,320" stroke="#fbbf24" stroke-width="3"/>
    <path d="M960,300 L980,280 M960,300 L980,320 M960,300 L940,280 M960,300 L940,320" stroke="#c0c0c0" stroke-width="3"/>
    <path d="M1360,300 L1380,280 M1360,300 L1380,320 M1360,300 L1340,280 M1360,300 L1340,320" stroke="#cd7f32" stroke-width="3"/>
  </g>
</svg>
