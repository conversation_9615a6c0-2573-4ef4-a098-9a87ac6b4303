<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,200 Q480,150 960,200 T1920,200" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)">
    雷霆出击：现场执行！
  </text>
  
  <!-- 背景装饰图案 -->
  <g opacity="0.1">
    <circle cx="200" cy="300" r="80" fill="#3b82f6"/>
    <circle cx="1720" cy="400" r="60" fill="#1e3a8a"/>
    <circle cx="150" cy="700" r="40" fill="#60a5fa"/>
    <circle cx="1800" cy="750" r="50" fill="#2563eb"/>
  </g>
  
  <!-- 中央大字 -->
  <rect x="260" y="420" width="1400" height="200" rx="20" fill="#ffffff" filter="url(#shadow)" opacity="0.95"/>
  <text x="960" y="520" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#1e3a8a">
    计划已经制定，现在，用执行力让它变成现实！
  </text>
  
  <!-- 底部关键词 -->
  <g transform="translate(960, 780)">
    <!-- 主动 -->
    <g transform="translate(-300, 0)">
      <rect x="-80" y="-40" width="160" height="80" rx="40" fill="#ef4444" filter="url(#shadow)"/>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ffffff">
        主动！
      </text>
    </g>
    
    <!-- 协作 -->
    <g transform="translate(0, 0)">
      <rect x="-80" y="-40" width="160" height="80" rx="40" fill="#10b981" filter="url(#shadow)"/>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ffffff">
        协作！
      </text>
    </g>
    
    <!-- 应变 -->
    <g transform="translate(300, 0)">
      <rect x="-80" y="-40" width="160" height="80" rx="40" fill="#f59e0b" filter="url(#shadow)"/>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ffffff">
        应变！
      </text>
    </g>
  </g>
  
  <!-- 装饰性箭头 -->
  <g transform="translate(960, 320)" opacity="0.3">
    <path d="M-100,0 L100,0 M80,-20 L100,0 L80,20" stroke="#3b82f6" stroke-width="4" fill="none"/>
  </g>
</svg>
