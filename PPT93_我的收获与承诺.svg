<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 背景装饰拼图效果 -->
  <g opacity="0.05">
    <rect x="100" y="100" width="200" height="150" rx="10" fill="#3b82f6"/>
    <rect x="350" y="120" width="180" height="130" rx="10" fill="#1e3a8a"/>
    <rect x="580" y="100" width="220" height="160" rx="10" fill="#60a5fa"/>
    <rect x="850" y="110" width="190" height="140" rx="10" fill="#2563eb"/>
    <rect x="1090" y="100" width="210" height="150" rx="10" fill="#3b82f6"/>
    <rect x="1350" y="120" width="170" height="130" rx="10" fill="#1e3a8a"/>
    <rect x="1570" y="100" width="200" height="160" rx="10" fill="#60a5fa"/>
    
    <rect x="120" y="300" width="180" height="140" rx="10" fill="#60a5fa"/>
    <rect x="350" y="320" width="200" height="120" rx="10" fill="#2563eb"/>
    <rect x="600" y="300" width="190" height="150" rx="10" fill="#3b82f6"/>
    <rect x="840" y="310" width="220" height="130" rx="10" fill="#1e3a8a"/>
    <rect x="1110" y="300" width="180" height="140" rx="10" fill="#60a5fa"/>
    <rect x="1340" y="320" width="200" height="120" rx="10" fill="#2563eb"/>
    <rect x="1590" y="300" width="190" height="150" rx="10" fill="#3b82f6"/>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)">
    我的收获与承诺
  </text>
  
  <!-- 两个对话框 -->
  <g transform="translate(960, 450)">
    <!-- 收获对话框 -->
    <g transform="translate(-300, -80)">
      <rect x="-250" y="-80" width="500" height="160" rx="20" fill="#ffffff" filter="url(#shadow)"/>
      <!-- 对话框尖角 -->
      <path d="M-20,80 L0,100 L20,80 Z" fill="#ffffff"/>
      <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#3b82f6">
        在这四天里，
      </text>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#3b82f6">
        你个人最大的收获是什么？
      </text>
    </g>
    
    <!-- 承诺对话框 -->
    <g transform="translate(300, 80)">
      <rect x="-250" y="-80" width="500" height="160" rx="20" fill="#ffffff" filter="url(#shadow)"/>
      <!-- 对话框尖角 -->
      <path d="M-20,-80 L0,-100 L20,-80 Z" fill="#ffffff"/>
      <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">
        面向未来，回到岗位后，
      </text>
      <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">
        你将为自己许下一个什么样的承诺？
      </text>
    </g>
  </g>
  
  <!-- 底部感悟文字 -->
  <g transform="translate(960, 750)">
    <rect x="-600" y="-60" width="1200" height="120" rx="20" fill="#f3f4f6" filter="url(#shadow)" opacity="0.8"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">
      真诚的分享，是对自己最好的总结；
    </text>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">
      郑重的承诺，是对未来最好的开启。
    </text>
  </g>
  
  <!-- 装饰性心形图案 -->
  <g opacity="0.2">
    <!-- 左侧心形 -->
    <g transform="translate(200, 400)">
      <path d="M0,10 C0,0 -10,-5 -15,0 C-20,-5 -30,0 -30,10 C-30,20 -15,35 0,50 C15,35 30,20 30,10 C30,0 20,-5 15,0 C10,-5 0,0 0,10 Z" fill="#ef4444"/>
    </g>
    
    <!-- 右侧心形 -->
    <g transform="translate(1720, 600)">
      <path d="M0,8 C0,0 -8,-4 -12,0 C-16,-4 -24,0 -24,8 C-24,16 -12,28 0,40 C12,28 24,16 24,8 C24,0 16,-4 12,0 C8,-4 0,0 0,8 Z" fill="#10b981"/>
    </g>
  </g>
  
  <!-- 装饰性思考泡泡 -->
  <g opacity="0.3">
    <circle cx="400" cy="300" r="15" fill="#3b82f6"/>
    <circle cx="430" cy="280" r="10" fill="#3b82f6"/>
    <circle cx="450" cy="260" r="6" fill="#3b82f6"/>
    
    <circle cx="1520" cy="350" r="12" fill="#1e3a8a"/>
    <circle cx="1540" cy="330" r="8" fill="#1e3a8a"/>
    <circle cx="1555" cy="315" r="5" fill="#1e3a8a"/>
  </g>
</svg>
