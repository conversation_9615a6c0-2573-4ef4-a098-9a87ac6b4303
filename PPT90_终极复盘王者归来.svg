<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fef3c7;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="6" stdDeviation="12" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 背景庆祝装饰 -->
  <g opacity="0.08">
    <circle cx="300" cy="200" r="100" fill="#fbbf24"/>
    <circle cx="1620" cy="250" r="80" fill="#dc2626"/>
    <circle cx="200" cy="500" r="60" fill="#10b981"/>
    <circle cx="1720" cy="600" r="70" fill="#3b82f6"/>
    <circle cx="150" cy="800" r="50" fill="#8b5cf6"/>
    <circle cx="1800" cy="850" r="55" fill="#f59e0b"/>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#fbbf24" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#dc2626" stroke-width="3" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)" filter="url(#glow)">
    终极复盘：王者归来
  </text>
  
  <!-- 欢迎词 -->
  <g transform="translate(960, 400)">
    <rect x="-500" y="-80" width="1000" height="160" rx="30" fill="#ffffff" filter="url(#shadow)" opacity="0.95"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="#1e3a8a">
      欢迎我们的英雄，凯旋归来！
    </text>
  </g>
  
  <!-- 三个关键词 -->
  <g transform="translate(960, 650)">
    <!-- 分享战果 -->
    <g transform="translate(-350, 0)">
      <rect x="-120" y="-50" width="240" height="100" rx="50" fill="#dc2626" filter="url(#shadow)"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ffffff">
        分享战果
      </text>
    </g>
    
    <!-- 萃取经验 -->
    <g transform="translate(0, 0)">
      <rect x="-120" y="-50" width="240" height="100" rx="50" fill="#10b981" filter="url(#shadow)"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ffffff">
        萃取经验
      </text>
    </g>
    
    <!-- 接受荣耀 -->
    <g transform="translate(350, 0)">
      <rect x="-120" y="-50" width="240" height="100" rx="50" fill="#fbbf24" filter="url(#shadow)"/>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#ffffff">
        接受荣耀
      </text>
    </g>
  </g>
  
  <!-- 装饰性庆祝元素 -->
  <g opacity="0.3">
    <!-- 左侧庆祝彩带 -->
    <g transform="translate(200, 300)">
      <path d="M0,0 Q20,50 0,100 Q-20,150 0,200" stroke="#dc2626" stroke-width="8" fill="none"/>
      <path d="M30,20 Q50,70 30,120 Q10,170 30,220" stroke="#fbbf24" stroke-width="6" fill="none"/>
    </g>
    
    <!-- 右侧庆祝彩带 -->
    <g transform="translate(1720, 350)">
      <path d="M0,0 Q-20,50 0,100 Q20,150 0,200" stroke="#10b981" stroke-width="8" fill="none"/>
      <path d="M-30,20 Q-50,70 -30,120 Q-10,170 -30,220" stroke="#3b82f6" stroke-width="6" fill="none"/>
    </g>
  </g>
  
  <!-- 装饰性星星爆炸效果 -->
  <g opacity="0.4">
    <!-- 中央星星爆炸 -->
    <g transform="translate(960, 300)">
      <path d="M0,-30 L9,-9 L30,-9 L15,3 L24,24 L0,12 L-24,24 L-15,3 L-30,-9 L-9,-9 Z" fill="#fbbf24"/>
      <path d="M-40,-10 L-35,-5 L-30,-10 L-35,-15 Z" fill="#fbbf24"/>
      <path d="M40,-10 L35,-5 L30,-10 L35,-15 Z" fill="#fbbf24"/>
      <path d="M-10,-40 L-5,-35 L-10,-30 L-15,-35 Z" fill="#fbbf24"/>
      <path d="M10,40 L5,35 L10,30 L15,35 Z" fill="#fbbf24"/>
    </g>
  </g>
  
  <!-- 底部装饰波浪 -->
  <g opacity="0.2">
    <path d="M0,800 Q240,750 480,800 T960,800 Q1200,750 1440,800 T1920,800" stroke="#dc2626" stroke-width="4" fill="none"/>
    <path d="M0,820 Q240,770 480,820 T960,820 Q1200,770 1440,820 T1920,820" stroke="#fbbf24" stroke-width="3" fill="none"/>
  </g>
</svg>
