<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,120 Q480,80 960,120 T1920,120" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="100" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)">
    行动SOP："上门服务十大标准步骤"
  </text>
  
  <!-- 十大步骤列表 -->
  <g transform="translate(160, 180)">
    <!-- 左列 -->
    <g>
      <!-- 步骤1 -->
      <g transform="translate(0, 0)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#ef4444"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">1</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">准时到达，专业开场</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">(出示工牌，穿鞋套)</text>
      </g>
      
      <!-- 步骤2 -->
      <g transform="translate(0, 80)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#f59e0b"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">2</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">赠送小礼，拉近关系</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">(如：插座安全盖)</text>
      </g>
      
      <!-- 步骤3 -->
      <g transform="translate(0, 160)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#10b981"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">3</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">重申目的，确认需求</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">("王先生，今天主要是根据您昨天提到的…")</text>
      </g>
      
      <!-- 步骤4 -->
      <g transform="translate(0, 240)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#3b82f6"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">4</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">全屋勘察，展现专业</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">(使用工具，边测边记录)</text>
      </g>
      
      <!-- 步骤5 -->
      <g transform="translate(0, 320)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#8b5cf6"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">5</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">家庭访谈，深度挖需</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">(与所有家庭成员互动)</text>
      </g>
    </g>
    
    <!-- 右列 -->
    <g transform="translate(800, 0)">
      <!-- 步骤6 -->
      <g transform="translate(0, 0)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#06b6d4"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">6</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">方案呈现，价值导向</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">(先讲价值，后讲价格)</text>
      </g>
      
      <!-- 步骤7 -->
      <g transform="translate(0, 80)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#84cc16"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">7</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">引导体验，建立感知</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">(用平板电脑演示APP)</text>
      </g>
      
      <!-- 步骤8 -->
      <g transform="translate(0, 160)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#f97316"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">8</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">商务谈判，灵活应对</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">(处理异议，促成签约)</text>
      </g>
      
      <!-- 步骤9 -->
      <g transform="translate(0, 240)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#ec4899"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">9</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">服务闭环，超越期望</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">(建专属群，告知安装流程)</text>
      </g>
      
      <!-- 步骤10 -->
      <g transform="translate(0, 320)">
        <rect x="0" y="0" width="750" height="70" rx="10" fill="#ffffff" filter="url(#shadow)"/>
        <circle cx="35" cy="35" r="20" fill="#6366f1"/>
        <text x="35" y="42" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" font-weight="bold" fill="#ffffff">10</text>
        <text x="70" y="30" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#1e3a8a">礼貌告辞，留下好感</text>
        <text x="70" y="55" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">(整理现场，真诚感谢)</text>
      </g>
    </g>
  </g>
  
  <!-- 底部装饰 -->
  <path d="M0,950 Q480,1000 960,950 T1920,950" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 左右装饰元素 -->
  <g opacity="0.1">
    <circle cx="80" cy="400" r="40" fill="#3b82f6"/>
    <circle cx="1840" cy="450" r="35" fill="#10b981"/>
  </g>
</svg>
