<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)">
    今日使命：拿下订单，更要赢得人心
  </text>
  
  <!-- 靶心图 -->
  <g transform="translate(960, 540)">
    <!-- 外环 - 基础使命 -->
    <circle cx="0" cy="0" r="280" fill="#fef3c7" stroke="#f59e0b" stroke-width="4" filter="url(#shadow)" opacity="0.8"/>
    <text x="0" y="-200" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#92400e">
      外环（基础使命）
    </text>
    <text x="0" y="-160" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#92400e">
      严格执行"上门服务十大标准步骤"，
    </text>
    <text x="0" y="-130" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#92400e">
      展现无懈可击的专业形象。
    </text>
    
    <!-- 中环 - 核心使命 -->
    <circle cx="0" cy="0" r="180" fill="#dbeafe" stroke="#3b82f6" stroke-width="4" filter="url(#shadow)" opacity="0.9"/>
    <text x="0" y="-80" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e40af">
      中环（核心使命）
    </text>
    <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#1e40af">
      精准呈现"个性化解决方案"，
    </text>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#1e40af">
      让客户感觉"这就是我想要的"。
    </text>
    
    <!-- 靶心 - 决胜使命 -->
    <circle cx="0" cy="0" r="80" fill="#fecaca" stroke="#dc2626" stroke-width="4" filter="url(#shadow)"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#991b1b">
      靶心（决胜使命）
    </text>
    <text x="0" y="20" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#991b1b">
      成功完成"签约闭环"，
    </text>
    <text x="0" y="45" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#991b1b">
      并赢得客户的高度信任和满意。
    </text>
    
    <!-- 靶心中心点 -->
    <circle cx="0" cy="0" r="8" fill="#dc2626"/>
  </g>
  
  <!-- 装饰性箭头指向靶心 -->
  <g opacity="0.4">
    <g transform="translate(600, 400) rotate(-30)">
      <path d="M0,0 L100,0 M80,-15 L100,0 L80,15" stroke="#dc2626" stroke-width="4" fill="none"/>
    </g>
    <g transform="translate(1320, 400) rotate(30)">
      <path d="M0,0 L-100,0 M-80,-15 L-100,0 L-80,15" stroke="#dc2626" stroke-width="4" fill="none"/>
    </g>
    <g transform="translate(960, 200) rotate(90)">
      <path d="M0,0 L100,0 M80,-15 L100,0 L80,15" stroke="#dc2626" stroke-width="4" fill="none"/>
    </g>
  </g>
  
  <!-- 左右装饰 -->
  <g opacity="0.1">
    <circle cx="150" cy="350" r="60" fill="#dc2626"/>
    <circle cx="100" cy="550" r="40" fill="#3b82f6"/>
    <circle cx="200" cy="750" r="50" fill="#f59e0b"/>
    
    <circle cx="1770" cy="400" r="55" fill="#f59e0b"/>
    <circle cx="1820" cy="600" r="35" fill="#dc2626"/>
    <circle cx="1720" cy="800" r="45" fill="#3b82f6"/>
  </g>
</svg>
