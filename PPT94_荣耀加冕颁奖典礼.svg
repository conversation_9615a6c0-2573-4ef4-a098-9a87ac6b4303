<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="spotlightGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.4" />
      <stop offset="70%" style="stop-color:#fbbf24;stop-opacity:0.2" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:0" />
    </radialGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 金色幕布背景 -->
  <g opacity="0.1">
    <rect x="0" y="0" width="1920" height="200" fill="url(#titleGradient)"/>
    <rect x="0" y="880" width="1920" height="200" fill="url(#titleGradient)"/>
  </g>
  
  <!-- 聚光灯效果 -->
  <ellipse cx="960" cy="540" rx="600" ry="400" fill="url(#spotlightGradient)" filter="url(#glow)"/>
  <ellipse cx="960" cy="540" rx="400" ry="300" fill="url(#spotlightGradient)" filter="url(#glow)"/>
  <ellipse cx="960" cy="540" rx="200" ry="150" fill="url(#spotlightGradient)" filter="url(#glow)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#fbbf24" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#fbbf24" stroke-width="3" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)" filter="url(#glow)">
    荣耀加冕：颁奖典礼
  </text>
  
  <!-- 奖杯图标 -->
  <g transform="translate(960, 540)" filter="url(#shadow)">
    <!-- 奖杯主体 -->
    <ellipse cx="0" cy="0" rx="80" ry="60" fill="#fbbf24"/>
    <rect x="-60" y="-40" width="120" height="80" rx="10" fill="#f59e0b"/>
    
    <!-- 奖杯手柄 -->
    <ellipse cx="-90" cy="-10" rx="20" ry="30" fill="none" stroke="#f59e0b" stroke-width="8"/>
    <ellipse cx="90" cy="-10" rx="20" ry="30" fill="none" stroke="#f59e0b" stroke-width="8"/>
    
    <!-- 奖杯底座 -->
    <rect x="-40" y="40" width="80" height="20" rx="5" fill="#d97706"/>
    <rect x="-50" y="60" width="100" height="15" rx="7" fill="#92400e"/>
    
    <!-- 奖杯装饰 -->
    <circle cx="0" cy="-10" r="25" fill="#fef3c7"/>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" font-weight="bold" fill="#f59e0b">
      🏆
    </text>
  </g>
  
  <!-- 装饰性光芒 -->
  <g opacity="0.3">
    <!-- 上方光芒 -->
    <path d="M960,200 L940,300 L980,300 Z" fill="#fbbf24"/>
    <path d="M960,200 L920,320 L1000,320 Z" fill="#fbbf24" opacity="0.7"/>
    <path d="M960,200 L900,340 L1020,340 Z" fill="#fbbf24" opacity="0.5"/>
    
    <!-- 左右光芒 -->
    <path d="M700,540 L800,520 L800,560 Z" fill="#fbbf24"/>
    <path d="M1220,540 L1120,520 L1120,560 Z" fill="#fbbf24"/>
    
    <!-- 下方光芒 -->
    <path d="M960,780 L940,680 L980,680 Z" fill="#fbbf24"/>
  </g>
  
  <!-- 装饰性星星 -->
  <g opacity="0.4">
    <!-- 左上星星 -->
    <g transform="translate(300, 250)">
      <path d="M0,-25 L7.5,-7.5 L25,-7.5 L12.5,2.5 L20,20 L0,10 L-20,20 L-12.5,2.5 L-25,-7.5 L-7.5,-7.5 Z" fill="#fbbf24"/>
    </g>
    
    <!-- 右上星星 -->
    <g transform="translate(1620, 280)">
      <path d="M0,-20 L6,-6 L20,-6 L10,2 L16,16 L0,8 L-16,16 L-10,2 L-20,-6 L-6,-6 Z" fill="#fbbf24"/>
    </g>
    
    <!-- 左下星星 -->
    <g transform="translate(250, 750)">
      <path d="M0,-18 L5.4,-5.4 L18,-5.4 L9,1.8 L14.4,14.4 L0,7.2 L-14.4,14.4 L-9,1.8 L-18,-5.4 L-5.4,-5.4 Z" fill="#fbbf24"/>
    </g>
    
    <!-- 右下星星 -->
    <g transform="translate(1670, 800)">
      <path d="M0,-22 L6.6,-6.6 L22,-6.6 L11,2.2 L17.6,17.6 L0,8.8 L-17.6,17.6 L-11,2.2 L-22,-6.6 L-6.6,-6.6 Z" fill="#fbbf24"/>
    </g>
  </g>
  
  <!-- 装饰性彩带 -->
  <g opacity="0.2">
    <!-- 左侧彩带 -->
    <path d="M200,300 Q300,350 200,400 Q100,450 200,500 Q300,550 200,600" stroke="#dc2626" stroke-width="12" fill="none"/>
    <path d="M250,320 Q350,370 250,420 Q150,470 250,520 Q350,570 250,620" stroke="#fbbf24" stroke-width="10" fill="none"/>
    
    <!-- 右侧彩带 -->
    <path d="M1720,350 Q1620,400 1720,450 Q1820,500 1720,550 Q1620,600 1720,650" stroke="#10b981" stroke-width="12" fill="none"/>
    <path d="M1670,370 Q1570,420 1670,470 Q1770,520 1670,570 Q1570,620 1670,670" stroke="#3b82f6" stroke-width="10" fill="none"/>
  </g>
</svg>
