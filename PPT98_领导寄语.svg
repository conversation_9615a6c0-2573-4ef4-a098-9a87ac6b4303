<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="6" stdDeviation="12" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#e5e7eb" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#e5e7eb" stroke-width="3" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)" filter="url(#glow)">
    领导寄语
  </text>
  
  <!-- 领导照片和信息区域 -->
  <g transform="translate(960, 400)" filter="url(#shadow)">
    <!-- 背景卡片 -->
    <rect x="-500" y="-150" width="1000" height="300" rx="30" fill="#ffffff" opacity="0.95"/>
    
    <!-- 领导照片占位区 -->
    <g transform="translate(-300, 0)">
      <rect x="-100" y="-100" width="200" height="200" rx="20" fill="#f3f4f6" stroke="#3b82f6" stroke-width="3" stroke-dasharray="10,5"/>
      <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#6b7280">
        [领导照片]
      </text>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" fill="#9ca3af">
        公司领导
      </text>
    </g>
    
    <!-- 领导信息 -->
    <g transform="translate(100, 0)">
      <text x="0" y="-60" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">
        [领导姓名]
      </text>
      <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" fill="#374151">
        [职务]
      </text>
      
      <!-- 寄语内容占位 -->
      <rect x="-250" y="20" width="500" height="80" rx="10" fill="#f8fafc" stroke="#3b82f6" stroke-width="2" stroke-dasharray="5,5"/>
      <text x="0" y="50" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#6b7280">
        [领导寄语内容]
      </text>
      <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="18" fill="#9ca3af">
        现场填写或预设内容
      </text>
    </g>
  </g>
  
  <!-- 装饰性引号 -->
  <g opacity="0.2">
    <!-- 左上引号 -->
    <g transform="translate(300, 250)">
      <text x="0" y="0" font-family="serif" font-size="120" fill="#3b82f6">"</text>
    </g>
    
    <!-- 右下引号 -->
    <g transform="translate(1620, 650)">
      <text x="0" y="0" font-family="serif" font-size="120" fill="#3b82f6">"</text>
    </g>
  </g>
  
  <!-- 底部激励文字 -->
  <g transform="translate(960, 750)">
    <rect x="-600" y="-50" width="1200" height="100" rx="20" fill="#1e3a8a" filter="url(#shadow)" opacity="0.9"/>
    <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ffffff">
      让我们用最热烈的掌声，
    </text>
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#fbbf24">
      感谢领导的鼓励与寄语！
    </text>
  </g>
  
  <!-- 装饰性元素 -->
  <g opacity="0.1">
    <!-- 左侧装饰 -->
    <circle cx="150" cy="300" r="60" fill="#3b82f6"/>
    <circle cx="100" cy="500" r="40" fill="#1e3a8a"/>
    <circle cx="200" cy="700" r="50" fill="#60a5fa"/>
    
    <!-- 右侧装饰 -->
    <circle cx="1770" cy="350" r="55" fill="#60a5fa"/>
    <circle cx="1820" cy="550" r="35" fill="#3b82f6"/>
    <circle cx="1720" cy="750" r="45" fill="#1e3a8a"/>
  </g>
  
  <!-- 装饰性光芒效果 -->
  <g opacity="0.3">
    <path d="M960,200 L940,280 L980,280 Z" fill="#3b82f6"/>
    <path d="M960,200 L920,300 L1000,300 Z" fill="#3b82f6" opacity="0.7"/>
    <path d="M960,200 L900,320 L1020,320 Z" fill="#3b82f6" opacity="0.5"/>
  </g>
  
  <!-- 装饰性星星 -->
  <g opacity="0.4">
    <g transform="translate(200, 200)">
      <path d="M0,-15 L4.5,-4.5 L15,-4.5 L7.5,1.5 L12,12 L0,6 L-12,12 L-7.5,1.5 L-15,-4.5 L-4.5,-4.5 Z" fill="#3b82f6"/>
    </g>
    <g transform="translate(1720, 250)">
      <path d="M0,-12 L3.6,-3.6 L12,-3.6 L6,1.2 L9.6,9.6 L0,4.8 L-9.6,9.6 L-6,1.2 L-12,-3.6 L-3.6,-3.6 Z" fill="#3b82f6"/>
    </g>
    <g transform="translate(150, 850)">
      <path d="M0,-18 L5.4,-5.4 L18,-5.4 L9,1.8 L14.4,14.4 L0,7.2 L-14.4,14.4 L-9,1.8 L-18,-5.4 L-5.4,-5.4 Z" fill="#3b82f6"/>
    </g>
    <g transform="translate(1770, 900)">
      <path d="M0,-16 L4.8,-4.8 L16,-4.8 L8,1.6 L12.8,12.8 L0,6.4 L-12.8,12.8 L-8,1.6 L-16,-4.8 L-4.8,-4.8 Z" fill="#3b82f6"/>
    </g>
  </g>
</svg>
