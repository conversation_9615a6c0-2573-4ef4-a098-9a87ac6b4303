<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="6" stdDeviation="12" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#fbbf24" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#fbbf24" stroke-width="3" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)" filter="url(#glow)">
    团队最高荣誉：王牌战队
  </text>
  
  <!-- 奖杯/红旗特写 -->
  <g transform="translate(960, 350)" filter="url(#shadow)">
    <!-- 红旗背景 -->
    <rect x="-200" y="-100" width="400" height="200" rx="20" fill="#dc2626"/>
    
    <!-- 红旗装饰边框 -->
    <rect x="-200" y="-100" width="400" height="200" rx="20" fill="none" stroke="#fbbf24" stroke-width="6"/>
    
    <!-- 奖杯图标 -->
    <g transform="translate(0, -20)">
      <ellipse cx="0" cy="0" rx="60" ry="45" fill="#fbbf24"/>
      <rect x="-45" y="-30" width="90" height="60" rx="8" fill="#f59e0b"/>
      <ellipse cx="-70" cy="-8" rx="15" ry="22" fill="none" stroke="#f59e0b" stroke-width="6"/>
      <ellipse cx="70" cy="-8" rx="15" ry="22" fill="none" stroke="#f59e0b" stroke-width="6"/>
      <rect x="-30" y="30" width="60" height="15" rx="4" fill="#d97706"/>
      <rect x="-35" y="45" width="70" height="10" rx="5" fill="#92400e"/>
      <circle cx="0" cy="-8" r="18" fill="#fef3c7"/>
      <text x="0" y="-2" text-anchor="middle" font-family="Microsoft YaHei" font-size="16" font-weight="bold" fill="#f59e0b">🏆</text>
    </g>
    
    <!-- 红旗文字 -->
    <text x="0" y="80" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ffffff">
      王牌战队
    </text>
  </g>
  
  <!-- 获奖团队 -->
  <g transform="translate(960, 550)">
    <rect x="-150" y="-40" width="300" height="80" rx="20" fill="#ffffff" filter="url(#shadow)"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#dc2626">
      第 X 小组
    </text>
  </g>
  
  <!-- 颁奖词 -->
  <g transform="translate(960, 700)">
    <rect x="-600" y="-80" width="1200" height="160" rx="20" fill="#ffffff" filter="url(#shadow)" opacity="0.95"/>
    <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#1e3a8a">
      颁奖词
    </text>
    <text x="0" y="-5" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#374151">
      他们，在四天的征战中，配合默契，勇于拼搏，屡创佳绩，
    </text>
    <text x="0" y="25" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#374151">
      最终以XXX分的绝对优势，赢得了本次特训营的团队总冠军。
    </text>
    <text x="0" y="55" text-anchor="middle" font-family="Microsoft YaHei" font-size="22" fill="#374151">
      他们用行动诠释了什么叫"召之即来，来之能战，战之必胜"！
    </text>
  </g>
  
  <!-- 装饰性庆祝元素 -->
  <g opacity="0.3">
    <!-- 左侧庆祝烟花 -->
    <g transform="translate(300, 300)">
      <circle cx="0" cy="0" r="3" fill="#dc2626"/>
      <path d="M0,0 L-20,-30 M0,0 L20,-30 M0,0 L-30,0 M0,0 L30,0 M0,0 L-20,30 M0,0 L20,30" stroke="#dc2626" stroke-width="2"/>
      <circle cx="-20" cy="-30" r="2" fill="#fbbf24"/>
      <circle cx="20" cy="-30" r="2" fill="#fbbf24"/>
      <circle cx="-30" cy="0" r="2" fill="#fbbf24"/>
      <circle cx="30" cy="0" r="2" fill="#fbbf24"/>
      <circle cx="-20" cy="30" r="2" fill="#fbbf24"/>
      <circle cx="20" cy="30" r="2" fill="#fbbf24"/>
    </g>
    
    <!-- 右侧庆祝烟花 -->
    <g transform="translate(1620, 350)">
      <circle cx="0" cy="0" r="3" fill="#10b981"/>
      <path d="M0,0 L-25,-35 M0,0 L25,-35 M0,0 L-35,0 M0,0 L35,0 M0,0 L-25,35 M0,0 L25,35" stroke="#10b981" stroke-width="2"/>
      <circle cx="-25" cy="-35" r="2" fill="#3b82f6"/>
      <circle cx="25" cy="-35" r="2" fill="#3b82f6"/>
      <circle cx="-35" cy="0" r="2" fill="#3b82f6"/>
      <circle cx="35" cy="0" r="2" fill="#3b82f6"/>
      <circle cx="-25" cy="35" r="2" fill="#3b82f6"/>
      <circle cx="25" cy="35" r="2" fill="#3b82f6"/>
    </g>
  </g>
  
  <!-- 装饰性星星 -->
  <g opacity="0.4">
    <g transform="translate(200, 200)">
      <path d="M0,-20 L6,-6 L20,-6 L10,2 L16,16 L0,8 L-16,16 L-10,2 L-20,-6 L-6,-6 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1720, 250)">
      <path d="M0,-18 L5.4,-5.4 L18,-5.4 L9,1.8 L14.4,14.4 L0,7.2 L-14.4,14.4 L-9,1.8 L-18,-5.4 L-5.4,-5.4 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(150, 600)">
      <path d="M0,-15 L4.5,-4.5 L15,-4.5 L7.5,1.5 L12,12 L0,6 L-12,12 L-7.5,1.5 L-15,-4.5 L-4.5,-4.5 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1770, 650)">
      <path d="M0,-22 L6.6,-6.6 L22,-6.6 L11,2.2 L17.6,17.6 L0,8.8 L-17.6,17.6 L-11,2.2 L-22,-6.6 L-6.6,-6.6 Z" fill="#fbbf24"/>
    </g>
  </g>
  
  <!-- 底部装饰 -->
  <g opacity="0.2">
    <path d="M300,850 Q960,800 1620,850" stroke="#dc2626" stroke-width="4" fill="none"/>
    <path d="M300,870 Q960,820 1620,870" stroke="#fbbf24" stroke-width="3" fill="none"/>
  </g>
</svg>
