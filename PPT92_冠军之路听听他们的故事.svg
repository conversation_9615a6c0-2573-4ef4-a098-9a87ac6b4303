<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="spotlightGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:0.3" />
      <stop offset="70%" style="stop-color:#fbbf24;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:0" />
    </radialGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)">
    冠军之路：听听他们的故事
  </text>
  
  <!-- 聚光灯效果 -->
  <ellipse cx="960" cy="450" rx="400" ry="300" fill="url(#spotlightGradient)" filter="url(#glow)"/>
  
  <!-- 聚光灯中央区域 -->
  <g transform="translate(960, 400)">
    <!-- 冠军团队合影占位区 -->
    <rect x="-300" y="-100" width="600" height="200" rx="20" fill="#ffffff" stroke="#fbbf24" stroke-width="4" stroke-dasharray="10,5" filter="url(#shadow)"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">
      [此处贴上冠军团队合影]
    </text>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#fbbf24">
      🏆 总冠军团队 🏆
    </text>
  </g>
  
  <!-- 分享主题 -->
  <g transform="translate(960, 650)">
    <rect x="-500" y="-120" width="1000" height="240" rx="20" fill="#ffffff" filter="url(#shadow)"/>
    <text x="0" y="-80" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#1e3a8a">
      分享主题：
    </text>
    
    <!-- 主题1 -->
    <g transform="translate(0, -40)">
      <circle cx="-450" cy="0" r="12" fill="#dc2626"/>
      <text x="-430" y="5" font-family="Microsoft YaHei" font-size="24" fill="#374151">
        1. 我们遇到的最难的挑战是什么？
      </text>
    </g>
    
    <!-- 主题2 -->
    <g transform="translate(0, 0)">
      <circle cx="-450" cy="0" r="12" fill="#10b981"/>
      <text x="-430" y="5" font-family="Microsoft YaHei" font-size="24" fill="#374151">
        2. 我们是如何克服这个挑战的？（关键转折点）
      </text>
    </g>
    
    <!-- 主题3 -->
    <g transform="translate(0, 40)">
      <circle cx="-450" cy="0" r="12" fill="#3b82f6"/>
      <text x="-430" y="5" font-family="Microsoft YaHei" font-size="24" fill="#374151">
        3. 我们认为，这次成功的核心关键是什么？（可复制的经验）
      </text>
    </g>
  </g>
  
  <!-- 装饰性聚光灯光束 -->
  <g opacity="0.1">
    <path d="M960,100 L860,350 L1060,350 Z" fill="#fbbf24"/>
    <path d="M960,100 L800,350 L1120,350 Z" fill="#fbbf24" opacity="0.5"/>
  </g>
  
  <!-- 装饰性星星 -->
  <g opacity="0.3">
    <!-- 左侧星星 -->
    <g transform="translate(300, 300)">
      <path d="M0,-20 L6,-6 L20,-6 L10,2 L16,16 L0,8 L-16,16 L-10,2 L-20,-6 L-6,-6 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(200, 500)">
      <path d="M0,-15 L4.5,-4.5 L15,-4.5 L7.5,1.5 L12,12 L0,6 L-12,12 L-7.5,1.5 L-15,-4.5 L-4.5,-4.5 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(250, 700)">
      <path d="M0,-12 L3.6,-3.6 L12,-3.6 L6,1.2 L9.6,9.6 L0,4.8 L-9.6,9.6 L-6,1.2 L-12,-3.6 L-3.6,-3.6 Z" fill="#fbbf24"/>
    </g>
    
    <!-- 右侧星星 -->
    <g transform="translate(1620, 350)">
      <path d="M0,-20 L6,-6 L20,-6 L10,2 L16,16 L0,8 L-16,16 L-10,2 L-20,-6 L-6,-6 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1720, 550)">
      <path d="M0,-15 L4.5,-4.5 L15,-4.5 L7.5,1.5 L12,12 L0,6 L-12,12 L-7.5,1.5 L-15,-4.5 L-4.5,-4.5 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1670, 750)">
      <path d="M0,-12 L3.6,-3.6 L12,-3.6 L6,1.2 L9.6,9.6 L0,4.8 L-9.6,9.6 L-6,1.2 L-12,-3.6 L-3.6,-3.6 Z" fill="#fbbf24"/>
    </g>
  </g>
  
  <!-- 底部装饰线 -->
  <g opacity="0.3">
    <path d="M400,900 Q960,850 1520,900" stroke="#fbbf24" stroke-width="4" fill="none"/>
  </g>
</svg>
