<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="subtitleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#374151;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6b7280;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="12" flood-color="#000000" flood-opacity="0.15"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 背景装饰山峰轮廓 -->
  <g opacity="0.08">
    <path d="M0,600 L200,400 L400,500 L600,300 L800,450 L1000,250 L1200,400 L1400,200 L1600,350 L1800,150 L1920,300 L1920,1080 L0,1080 Z" fill="#1e3a8a"/>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M0,200 Q480,150 960,200 T1920,200" stroke="#e5e7eb" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,880 Q480,930 960,880 T1920,880" stroke="#e5e7eb" stroke-width="3" fill="none" opacity="0.6"/>
  
  <!-- 主标题 -->
  <g transform="translate(960, 400)">
    <rect x="-500" y="-100" width="1000" height="200" rx="30" fill="#ffffff" filter="url(#shadow)" opacity="0.95"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)" filter="url(#glow)">
      第四天：上门服务 · 价值深耕
    </text>
  </g>
  
  <!-- 副标题 -->
  <g transform="translate(960, 550)">
    <rect x="-400" y="-40" width="800" height="80" rx="20" fill="#ffffff" filter="url(#shadow)" opacity="0.9"/>
    <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="42" font-weight="300" fill="url(#subtitleGradient)">
      从"销售"到"顾问"的终极蜕变
    </text>
  </g>
  
  <!-- 底部英文标识 -->
  <text x="960" y="950" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#9ca3af" opacity="0.8">
    Day 4: In-Home Service · Deepening Value
  </text>
  
  <!-- 装饰性光芒效果 -->
  <g opacity="0.1">
    <circle cx="960" cy="400" r="300" fill="none" stroke="#fbbf24" stroke-width="2"/>
    <circle cx="960" cy="400" r="400" fill="none" stroke="#fbbf24" stroke-width="1"/>
    <circle cx="960" cy="400" r="500" fill="none" stroke="#fbbf24" stroke-width="0.5"/>
  </g>
  
  <!-- 左右装饰元素 -->
  <g opacity="0.15">
    <circle cx="150" cy="300" r="80" fill="#3b82f6"/>
    <circle cx="200" cy="500" r="50" fill="#1e3a8a"/>
    <circle cx="100" cy="700" r="60" fill="#60a5fa"/>
    
    <circle cx="1770" cy="350" r="70" fill="#60a5fa"/>
    <circle cx="1820" cy="550" r="45" fill="#2563eb"/>
    <circle cx="1720" cy="750" r="55" fill="#3b82f6"/>
  </g>
  
  <!-- 顶部装饰线条 -->
  <g opacity="0.3">
    <path d="M400,100 Q960,50 1520,100" stroke="#3b82f6" stroke-width="3" fill="none"/>
  </g>
</svg>
