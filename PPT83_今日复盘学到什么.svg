<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)">
    今日复盘：我们学到了什么？
  </text>
  
  <!-- 三个核心议题 -->
  <g transform="translate(960, 400)">
    <!-- 议题1 -->
    <g transform="translate(0, -120)">
      <rect x="-450" y="-80" width="900" height="160" rx="20" fill="#ffffff" filter="url(#shadow)"/>
      <circle cx="-380" cy="0" r="30" fill="#ef4444"/>
      <text x="-380" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ffffff">1</text>
      <text x="-320" y="-30" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">
        战果汇报：数据说话，我们今天的KPI完成了吗？
      </text>
    </g>
    
    <!-- 议题2 -->
    <g transform="translate(0, 80)">
      <rect x="-450" y="-100" width="900" height="200" rx="20" fill="#ffffff" filter="url(#shadow)"/>
      <circle cx="-380" cy="0" r="30" fill="#10b981"/>
      <text x="-380" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ffffff">2</text>
      <text x="-320" y="-50" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">
        "金句"与"地雷"分享：
      </text>
      <text x="-300" y="-10" font-family="Microsoft YaHei" font-size="28" fill="#374151">
        • 今天最有效的一句"开场白"是什么？
      </text>
      <text x="-300" y="30" font-family="Microsoft YaHei" font-size="28" fill="#374151">
        • 今天踩到的最大的一个"坑"是什么？
      </text>
    </g>
    
    <!-- 议题3 -->
    <g transform="translate(0, 300)">
      <rect x="-450" y="-100" width="900" height="200" rx="20" fill="#ffffff" filter="url(#shadow)"/>
      <circle cx="-380" cy="0" r="30" fill="#f59e0b"/>
      <text x="-380" y="8" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ffffff">3</text>
      <text x="-320" y="-50" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">
        关键人链接：
      </text>
      <text x="-300" y="-10" font-family="Microsoft YaHei" font-size="28" fill="#374151">
        • 我们和社区物业建立联系了吗？
      </text>
      <text x="-300" y="30" font-family="Microsoft YaHei" font-size="28" fill="#374151">
        • 下一步的合作突破口在哪里？
      </text>
    </g>
  </g>
  
  <!-- 左侧装饰 -->
  <g opacity="0.1">
    <circle cx="150" cy="350" r="70" fill="#3b82f6"/>
    <circle cx="100" cy="550" r="45" fill="#1e3a8a"/>
    <circle cx="200" cy="750" r="35" fill="#60a5fa"/>
  </g>
  
  <!-- 右侧装饰 -->
  <g opacity="0.1">
    <circle cx="1770" cy="400" r="60" fill="#60a5fa"/>
    <circle cx="1820" cy="600" r="40" fill="#2563eb"/>
    <circle cx="1720" cy="800" r="50" fill="#3b82f6"/>
  </g>
</svg>
