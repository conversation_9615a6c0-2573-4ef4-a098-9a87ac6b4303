<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dayGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 背景装饰图案 -->
  <g opacity="0.05">
    <rect x="200" y="200" width="1520" height="680" rx="40" fill="#1e3a8a"/>
  </g>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)">
    明日预告：终极决战！
  </text>
  
  <!-- 中央大字标题 -->
  <g transform="translate(960, 450)">
    <rect x="-400" y="-80" width="800" height="160" rx="20" fill="#ffffff" filter="url(#shadow)" opacity="0.95"/>
    <text x="0" y="-20" text-anchor="middle" font-family="Microsoft YaHei" font-size="56" font-weight="bold" fill="url(#dayGradient)">
      Day 4: 上门服务 · 价值深耕
    </text>
  </g>
  
  <!-- 三个关键词 -->
  <g transform="translate(960, 650)">
    <!-- 专业 -->
    <g transform="translate(-300, 0)">
      <rect x="-80" y="-40" width="160" height="80" rx="40" fill="#3b82f6" filter="url(#shadow)"/>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ffffff">
        专业
      </text>
    </g>
    
    <!-- 信任 -->
    <g transform="translate(0, 0)">
      <rect x="-80" y="-40" width="160" height="80" rx="40" fill="#10b981" filter="url(#shadow)"/>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ffffff">
        信任
      </text>
    </g>
    
    <!-- 大单 -->
    <g transform="translate(300, 0)">
      <rect x="-80" y="-40" width="160" height="80" rx="40" fill="#f59e0b" filter="url(#shadow)"/>
      <text x="0" y="10" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#ffffff">
        大单
      </text>
    </g>
  </g>
  
  <!-- 装饰性元素 -->
  <g opacity="0.2">
    <circle cx="200" cy="300" r="60" fill="#ef4444"/>
    <circle cx="1720" cy="350" r="50" fill="#3b82f6"/>
    <circle cx="150" cy="750" r="40" fill="#10b981"/>
    <circle cx="1800" cy="800" r="45" fill="#f59e0b"/>
  </g>
  
  <!-- 装饰性箭头指向明天 -->
  <g transform="translate(960, 800)" opacity="0.4">
    <path d="M-150,0 L150,0 M130,-30 L150,0 L130,30" stroke="#dc2626" stroke-width="6" fill="none"/>
    <text x="0" y="-50" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#dc2626">
      明天见！
    </text>
  </g>
</svg>
