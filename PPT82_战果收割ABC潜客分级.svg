<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="funnelGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#60a5fa;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:0.8" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#e5e7eb" stroke-width="2" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="120" text-anchor="middle" font-family="Microsoft YaHei" font-size="64" font-weight="bold" fill="url(#titleGradient)">
    战果收割：把沙子变成金子
  </text>
  
  <!-- 副标题 -->
  <text x="960" y="180" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" fill="#6b7280">
    核心工具：《A/B/C潜客分级与跟进策略》
  </text>
  
  <!-- 漏斗图 -->
  <g transform="translate(960, 540)">
    <!-- C类线索 - 顶端最宽 -->
    <g>
      <path d="M-300,0 L300,0 L250,-80 L-250,-80 Z" fill="#60a5fa" filter="url(#shadow)" opacity="0.9"/>
      <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ffffff">
        C类线索 (Cold Lead)
      </text>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#ffffff">
        "潜在机会者"
      </text>
      <text x="350" y="-40" font-family="Microsoft YaHei" font-size="20" fill="#374151">
        策略：纳入CRM，长期、低频度触达
      </text>
    </g>
    
    <!-- B类线索 - 中间 -->
    <g transform="translate(0, 120)">
      <path d="M-200,0 L200,0 L150,-80 L-150,-80 Z" fill="#3b82f6" filter="url(#shadow)" opacity="0.9"/>
      <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ffffff">
        B类线索 (Warm Lead)
      </text>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#ffffff">
        "理性思考者"
      </text>
      <text x="250" y="-30" font-family="Microsoft YaHei" font-size="20" fill="#374151">
        策略：拉入微信群，定期培育，
      </text>
      <text x="250" y="-5" font-family="Microsoft YaHei" font-size="20" fill="#374151">
        发送案例/优惠
      </text>
    </g>
    
    <!-- A类线索 - 底端最窄但价值最高 -->
    <g transform="translate(0, 240)">
      <path d="M-100,0 L100,0 L80,-80 L-80,-80 Z" fill="#1e3a8a" filter="url(#shadow)" opacity="0.9"/>
      <text x="0" y="-40" text-anchor="middle" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#ffffff">
        A类线索 (Hot Lead)
      </text>
      <text x="0" y="-10" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#ffffff">
        "立即行动者"
      </text>
      <text x="150" y="-30" font-family="Microsoft YaHei" font-size="20" fill="#374151">
        策略：24小时内必须电话跟进，
      </text>
      <text x="150" y="-5" font-family="Microsoft YaHei" font-size="20" fill="#374151">
        敲定上门时间！
      </text>
    </g>
    
    <!-- 价值标识 -->
    <g transform="translate(0, 320)">
      <rect x="-120" y="-30" width="240" height="60" rx="30" fill="#fbbf24" filter="url(#shadow)"/>
      <text x="0" y="5" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#ffffff">
        价值最高！
      </text>
    </g>
  </g>
  
  <!-- 左侧装饰 -->
  <g opacity="0.1">
    <circle cx="150" cy="400" r="60" fill="#3b82f6"/>
    <circle cx="100" cy="600" r="40" fill="#1e3a8a"/>
  </g>
  
  <!-- 右侧装饰 -->
  <g opacity="0.1">
    <circle cx="1770" cy="450" r="50" fill="#60a5fa"/>
    <circle cx="1820" cy="650" r="35" fill="#2563eb"/>
  </g>
</svg>
