<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="titleGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#fbbf24;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="frameGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="6" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#bgGradient)"/>
  
  <!-- 装饰性弧线 -->
  <path d="M0,150 Q480,100 960,150 T1920,150" stroke="#fbbf24" stroke-width="3" fill="none" opacity="0.6"/>
  <path d="M0,930 Q480,980 960,930 T1920,930" stroke="#dc2626" stroke-width="3" fill="none" opacity="0.6"/>
  
  <!-- 标题 -->
  <text x="960" y="150" text-anchor="middle" font-family="Microsoft YaHei" font-size="72" font-weight="bold" fill="url(#titleGradient)" filter="url(#glow)">
    定格荣耀瞬间
  </text>
  
  <!-- 巨大的相框 -->
  <g transform="translate(960, 540)" filter="url(#shadow)">
    <!-- 相框外框 -->
    <rect x="-500" y="-250" width="1000" height="500" rx="30" fill="url(#frameGradient)" stroke="#92400e" stroke-width="8"/>
    
    <!-- 相框内框 -->
    <rect x="-460" y="-210" width="920" height="420" rx="20" fill="#ffffff"/>
    
    <!-- 相框内容区域 -->
    <rect x="-440" y="-190" width="880" height="380" rx="15" fill="#f8fafc" stroke="#e5e7eb" stroke-width="2" stroke-dasharray="10,5"/>
    
    <!-- 相框标题 -->
    <text x="0" y="-120" text-anchor="middle" font-family="Microsoft YaHei" font-size="48" font-weight="bold" fill="#dc2626">
      聚势·智变·赢未来
    </text>
    
    <!-- 副标题 -->
    <text x="0" y="-60" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#1e3a8a">
      王牌战队，合影留念
    </text>
    
    <!-- 合影占位区域 -->
    <rect x="-350" y="-20" width="700" height="150" rx="10" fill="#ffffff" stroke="#fbbf24" stroke-width="3" stroke-dasharray="15,10"/>
    <text x="0" y="30" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" fill="#6b7280">
      [团队大合影区域]
    </text>
    <text x="0" y="60" text-anchor="middle" font-family="Microsoft YaHei" font-size="20" fill="#9ca3af">
      现场拍摄后贴入
    </text>
    
    <!-- 相框装饰角落 -->
    <g opacity="0.6">
      <!-- 左上角装饰 -->
      <path d="M-480,-230 L-450,-230 L-450,-200 M-480,-230 L-480,-200 L-450,-200" stroke="#92400e" stroke-width="4" fill="none"/>
      
      <!-- 右上角装饰 -->
      <path d="M480,-230 L450,-230 L450,-200 M480,-230 L480,-200 L450,-200" stroke="#92400e" stroke-width="4" fill="none"/>
      
      <!-- 左下角装饰 -->
      <path d="M-480,230 L-450,230 L-450,200 M-480,230 L-480,200 L-450,200" stroke="#92400e" stroke-width="4" fill="none"/>
      
      <!-- 右下角装饰 -->
      <path d="M480,230 L450,230 L450,200 M480,230 L480,200 L450,200" stroke="#92400e" stroke-width="4" fill="none"/>
    </g>
  </g>
  
  <!-- 装饰性相机图标 -->
  <g transform="translate(200, 300)" opacity="0.3">
    <rect x="-30" y="-20" width="60" height="40" rx="8" fill="#374151"/>
    <circle cx="0" cy="0" r="15" fill="#6b7280"/>
    <circle cx="0" cy="0" r="10" fill="#9ca3af"/>
    <rect x="-10" y="-25" width="20" height="8" rx="4" fill="#374151"/>
  </g>
  
  <g transform="translate(1720, 350)" opacity="0.3">
    <rect x="-25" y="-15" width="50" height="30" rx="6" fill="#374151"/>
    <circle cx="0" cy="0" r="12" fill="#6b7280"/>
    <circle cx="0" cy="0" r="8" fill="#9ca3af"/>
    <rect x="-8" y="-20" width="16" height="6" rx="3" fill="#374151"/>
  </g>
  
  <!-- 装饰性闪光效果 -->
  <g opacity="0.4">
    <!-- 左侧闪光 -->
    <g transform="translate(300, 400)">
      <path d="M0,-20 L5,0 L0,20 L-5,0 Z" fill="#fbbf24"/>
      <path d="M-20,0 L0,5 L20,0 L0,-5 Z" fill="#fbbf24"/>
    </g>
    
    <!-- 右侧闪光 -->
    <g transform="translate(1620, 450)">
      <path d="M0,-15 L4,0 L0,15 L-4,0 Z" fill="#fbbf24"/>
      <path d="M-15,0 L0,4 L15,0 L0,-4 Z" fill="#fbbf24"/>
    </g>
    
    <!-- 上方闪光 -->
    <g transform="translate(960, 250)">
      <path d="M0,-25 L6,0 L0,25 L-6,0 Z" fill="#dc2626"/>
      <path d="M-25,0 L0,6 L25,0 L0,-6 Z" fill="#dc2626"/>
    </g>
  </g>
  
  <!-- 装饰性星星 -->
  <g opacity="0.3">
    <g transform="translate(150, 200)">
      <path d="M0,-15 L4.5,-4.5 L15,-4.5 L7.5,1.5 L12,12 L0,6 L-12,12 L-7.5,1.5 L-15,-4.5 L-4.5,-4.5 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1770, 200)">
      <path d="M0,-12 L3.6,-3.6 L12,-3.6 L6,1.2 L9.6,9.6 L0,4.8 L-9.6,9.6 L-6,1.2 L-12,-3.6 L-3.6,-3.6 Z" fill="#dc2626"/>
    </g>
    <g transform="translate(100, 800)">
      <path d="M0,-18 L5.4,-5.4 L18,-5.4 L9,1.8 L14.4,14.4 L0,7.2 L-14.4,14.4 L-9,1.8 L-18,-5.4 L-5.4,-5.4 Z" fill="#fbbf24"/>
    </g>
    <g transform="translate(1820, 850)">
      <path d="M0,-16 L4.8,-4.8 L16,-4.8 L8,1.6 L12.8,12.8 L0,6.4 L-12.8,12.8 L-8,1.6 L-16,-4.8 L-4.8,-4.8 Z" fill="#dc2626"/>
    </g>
  </g>
  
  <!-- 底部装饰线 -->
  <g opacity="0.3">
    <path d="M300,900 Q960,850 1620,900" stroke="#fbbf24" stroke-width="4" fill="none"/>
    <path d="M300,920 Q960,870 1620,920" stroke="#dc2626" stroke-width="3" fill="none"/>
  </g>
</svg>
